import { supabase } from '@/lib/supabase';

export interface Mission {
  id: number;
  mission_number: string;
  mission_name: string;
  mission_date: string;
  mission_status: string;
  user_id: number;
  commodity_id: number;
  location_id: number;
  budgeted_amount: number;
  actual_amount: number;
  remarks?: string;
  created_by: number;
  updated_by: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface MissionWithDetails extends Mission {
  commodity: {
    commodity_id: number;
    commodity_name: string;
    unit_of_measurement: string;
    remarks?: string;
  };
  location: {
    id: number;
    location_name: string;
    ward?: string;
    gps_latitude?: number;
    gps_longitude?: number;
  };
  creator: {
    email?: string;
  };
}

export class MissionsService {
  /**
   * Fetch assigned missions for the current user
   */
  static async getAssignedMissions(userId: string): Promise<{ success: boolean; data?: MissionWithDetails[]; error?: string }> {
    try {
      console.log('🔄 Fetching assigned missions for user:', userId);

      const { data: missions, error } = await supabase
        .from('mission')
        .select(`
          *,
          commodity:commodities!mission_commodity_id_fkey(commodity_id, commodity_name, unit_of_measurement, remarks),
          location:locations!mission_location_id_fkey(id, location_name, ward, gps_latitude, gps_longitude),
          creator:users!mission_created_by_fkey(email)
        `)
        .eq('user_id', parseInt(userId))
        .neq('mission_status', 'cancelled')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching missions:', error);
        return { success: false, error: 'Failed to fetch assigned missions' };
      }

      if (!missions || missions.length === 0) {
        console.log('ℹ️  No active missions found for user');
        return { success: true, data: [] };
      }

      console.log('✅ Successfully fetched missions:', missions.length);
      return { success: true, data: missions as MissionWithDetails[] };

    } catch (error) {
      console.error('❌ Exception in getAssignedMissions:', error);
      return { success: false, error: 'An unexpected error occurred while fetching missions' };
    }
  }

  /**
   * Calculate budget usage percentage
   */
  static calculateBudgetUsage(mission: MissionWithDetails): { percentage: number; status: 'low' | 'medium' | 'high' | 'exceeded' } {
    const budgeted = mission.budgeted_amount || 1;
    const actual = mission.actual_amount || 0;
    const percentage = Math.min((actual / budgeted) * 100, 100);

    let status: 'low' | 'medium' | 'high' | 'exceeded' = 'low';
    if (percentage >= 100) status = 'exceeded';
    else if (percentage >= 80) status = 'high';
    else if (percentage >= 50) status = 'medium';

    return { percentage, status };
  }

  /**
   * Format currency amount
   */
  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Get mission status based on mission_status field
   */
  static getMissionStatus(mission: MissionWithDetails): { status: string; color: string } {
    switch (mission.mission_status?.toLowerCase()) {
      case 'active':
      case 'in_progress':
        return { status: 'active', color: '#22c55e' };
      case 'completed':
        return { status: 'completed', color: '#3b82f6' };
      case 'cancelled':
        return { status: 'cancelled', color: '#ef4444' };
      case 'pending':
        return { status: 'pending', color: '#f59e0b' };
      default:
        return { status: mission.mission_status || 'unknown', color: '#64748b' };
    }
  }

  /**
   * Get mission title from mission data
   */
  static getMissionTitle(mission: MissionWithDetails): string {
    const commodityName = mission.commodity?.commodity_name || 'Unknown Commodity';
    const locationName = mission.location?.location_name || 'Unknown Location';
    return `${commodityName} Purchase - ${locationName}`;
  }
}
