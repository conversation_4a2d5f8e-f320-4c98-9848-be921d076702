# DCBuyer PWA System Design Document

## 1. Overview

DCBuyer is an offline-first commodity trading and purchasing management system built as a Progressive Web App (PWA) using HTML, CSS, JavaScript, and Firebase. The system provides real-time data synchronization, offline capabilities, and robust transaction management for agricultural marketplaces.

### 1.1 Purpose
- Streamline commodity purchasing processes with offline-first approach
- Provide role-based access control with Firebase Authentication
- Enable offline-first operations with automatic synchronization
- Maintain comprehensive audit trails and transaction records
- Support real-time updates and cross-device synchronization

### 1.2 Scope
The system covers user management, customer management, commodity management, buyer assignments, and transaction processing using a PWA architecture with Firebase backend services.

### 1.3 Architecture Philosophy
- **Offline-First**: All operations work offline with automatic sync when online
- **Progressive Web App**: Installable, app-like experience across all devices
- **Real-time Sync**: Instant data synchronization using Firebase Firestore
- **Serverless Backend**: Firebase services for scalable, managed infrastructure
- **Modern Web Standards**: Service Workers, Web App Manifest, responsive design

## 2. System Architecture

### 2.1 High-Level Architecture (PWA + Firebase)
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile PWA    │    │   Desktop PWA   │    │   Admin Panel   │
│   (HTML/CSS/JS) │    │   (HTML/CSS/JS) │    │   (HTML/CSS/JS) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │ Firebase SDK
         ┌─────────────────────────────────────────────────┐
         │              Firebase Services                  │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Firebase Auth│  │ Firestore   │  │Cloud Func││
         │  │             │  │ (Database)  │  │(Backend) ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Cloud Storage│  │ Analytics   │  │Messaging ││
         │  │(Files)      │  │             │  │(Push)    ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Client Local Storage               │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │  IndexedDB  │  │Service Worker│  │Local Files││
         │  │(Offline DB) │  │(Cache/Sync) │  │(Cache)   ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

**Frontend (PWA):**
- HTML5 with semantic markup and accessibility features
- CSS3 with modern features (Grid, Flexbox, Custom Properties)
- Vanilla JavaScript (ES6+) with modern APIs
- Service Worker for offline functionality and caching
- Web App Manifest for PWA installation
- IndexedDB for offline data persistence
- Local Storage for app settings and preferences

**Backend (Firebase Services):**
- **Firebase Authentication**: User management with multiple providers
- **Cloud Firestore**: NoSQL document database with offline support
- **Firebase Cloud Functions**: Serverless backend logic (Node.js/TypeScript)
- **Firebase Cloud Storage**: File and image storage
- **Firebase Analytics**: User behavior tracking and insights
- **Firebase Cloud Messaging**: Push notifications
- **Firebase Hosting**: Static web app hosting with CDN

**Development Tools:**
- **Firebase CLI**: Project management and deployment
- **Firebase Emulator Suite**: Local development and testing
- **Web Dev Tools**: Chrome DevTools, Lighthouse for PWA auditing
- **Build Tools**: Webpack, Vite, or native ES modules
- **Testing**: Jest, Cypress for end-to-end testing

**PWA Features:**
- **Offline-First**: Works completely offline with IndexedDB
- **Installable**: Add to home screen on mobile and desktop
- **Responsive**: Adaptive design for all screen sizes
- **Fast**: Service Worker caching for instant loading
- **Secure**: HTTPS required, secure authentication
- **Engaging**: Push notifications and app-like experience

## 3. Firebase Firestore Database Design

### 3.1 Collection Structure
```
firestore/
├── users/
│   └── {userId}/
│       ├── profile (document)
│       └── roles/ (subcollection)
│           └── {roleId} (document)
├── customers/
│   └── {customerId} (document)
├── commodities/
│   └── {commodityId} (document)
├── assignments/
│   └── {assignmentId} (document)
├── transactions/
│   └── {transactionId} (document)
├── roles/
│   └── {roleId} (document)
└── metadata/
    └── sync (document)
```

### 3.2 Firestore Document Schemas

#### Users Collection (`/users/{userId}`)
```json
{
  "userId": "firebase-auth-uid",
  "email": "<EMAIL>",
  "username": "unique_username",
  "profile": {
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890",
    "profileImageUrl": "gs://bucket/images/profile.jpg"
  },
  "status": {
    "isActive": true,
    "lastLogin": "2024-01-15T10:30:00Z",
    "isOnline": false
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "syncVersion": 5,
    "createdBy": "admin-user-id"
  }
}
```

#### User Roles Subcollection (`/users/{userId}/roles/{roleId}`)
```json
{
  "roleId": "buyer",
  "roleName": "Buyer",
  "permissions": ["read_commodities", "create_transactions"],
  "assignedAt": "2024-01-01T00:00:00Z",
  "assignedBy": "admin-user-id",
  "isActive": true,
  "restrictions": {
    "maxTransactionAmount": 10000,
    "allowedCommodities": ["commodity-1", "commodity-2"]
  }
}
```

#### Roles Collection (`/roles/{roleId}`)
```json
{
  "roleId": "buyer",
  "roleName": "Buyer",
  "description": "Can purchase commodities and manage transactions",
  "permissions": [
    "read_assigned_commodities",
    "create_transactions",
    "read_customers",
    "create_customers",
    "read_own_transactions"
  ],
  "restrictions": {
    "maxTransactionAmount": 10000,
    "requiresApproval": false,
    "canViewAllTransactions": false
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "isSystemRole": true
  }
}
```

#### Customers Collection (`/customers/{customerId}`)
```json
{
  "customerId": "CUST-2024-001",
  "businessId": "unique-business-identifier",
  "profile": {
    "name": "ABC Farm Ltd",
    "contactPerson": "Jane Smith",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "address": {
      "street": "123 Farm Road",
      "city": "Farmville",
      "state": "State",
      "zipCode": "12345",
      "country": "Country"
    }
  },
  "location": {
    "coordinates": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "address": "Formatted address string"
  },
  "status": {
    "isActive": true,
    "creditLimit": 50000,
    "paymentTerms": "net-30"
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "createdBy": "user-id",
    "syncVersion": 3
  }
}
```

#### Commodities Collection (`/commodities/{commodityId}`)
```json
{
  "commodityId": "COMM-MAIZE-001",
  "name": "Yellow Maize",
  "description": "Grade A yellow maize for livestock feed",
  "category": "grains",
  "specifications": {
    "grade": "A",
    "moistureContent": "14%",
    "purity": "99%"
  },
  "pricing": {
    "basePrice": 250.00,
    "currency": "USD",
    "unitOfMeasurement": "kg",
    "pricePerUnit": 0.25,
    "lastUpdated": "2024-01-15T00:00:00Z"
  },
  "inventory": {
    "availableQuantity": 10000,
    "reservedQuantity": 500,
    "unit": "kg"
  },
  "status": {
    "isActive": true,
    "isAvailable": true,
    "seasonality": "year-round"
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "syncVersion": 2
  }
}
```

#### BuyerCommodityAssignments Table
```sql
CREATE TABLE buyer_commodity_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    buyer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    commodity_id UUID NOT NULL REFERENCES commodities(id) ON DELETE CASCADE,

    -- Assignment details
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID NOT NULL REFERENCES users(id),
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE, -- Optional end date
    is_active BOOLEAN DEFAULT true,

    -- Purchase limits
    daily_purchase_limit DECIMAL(15, 2),
    weekly_purchase_limit DECIMAL(15, 2),
    monthly_purchase_limit DECIMAL(15, 2),
    max_transaction_amount DECIMAL(15, 2),
    min_transaction_amount DECIMAL(15, 2),

    -- Territory and restrictions
    territory VARCHAR(100), -- Geographic area
    allowed_customers JSONB, -- Specific customer IDs if restricted
    restricted_customers JSONB, -- Customers not allowed

    -- Performance tracking
    total_purchases_amount DECIMAL(15, 2) DEFAULT 0,
    total_transactions_count INTEGER DEFAULT 0,
    last_transaction_date TIMESTAMP,

    -- Metadata
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sync_version INTEGER DEFAULT 1,

    UNIQUE(buyer_id, commodity_id, effective_from)
);

-- Indexes for performance
CREATE INDEX idx_assignments_buyer_id ON buyer_commodity_assignments(buyer_id);
CREATE INDEX idx_assignments_commodity_id ON buyer_commodity_assignments(commodity_id);
CREATE INDEX idx_assignments_active ON buyer_commodity_assignments(is_active);
CREATE INDEX idx_assignments_effective ON buyer_commodity_assignments(effective_from, effective_to);
CREATE INDEX idx_assignments_assigned_by ON buyer_commodity_assignments(assigned_by);
```

#### Transactions Table
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_number VARCHAR(50) UNIQUE NOT NULL,

    -- Parties involved
    buyer_id UUID NOT NULL REFERENCES users(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    commodity_id UUID NOT NULL REFERENCES commodities(id),

    -- Transaction details
    quantity DECIMAL(12, 4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(12, 4) NOT NULL,
    subtotal DECIMAL(15, 2) NOT NULL,
    tax_amount DECIMAL(15, 2) DEFAULT 0,
    discount_amount DECIMAL(15, 2) DEFAULT 0,
    total_amount DECIMAL(15, 2) NOT NULL,

    -- Payment information
    amount_paid DECIMAL(15, 2) NOT NULL,
    balance_due DECIMAL(15, 2) DEFAULT 0,
    payment_method VARCHAR(30), -- cash, bank_transfer, mobile_money, credit
    payment_reference VARCHAR(100), -- Reference number for electronic payments
    payment_status VARCHAR(20) DEFAULT 'completed', -- pending, completed, partial, failed

    -- Quality and grading
    quality_grade VARCHAR(10),
    quality_notes TEXT,
    moisture_content DECIMAL(5, 2),
    purity_percentage DECIMAL(5, 2),
    quality_checked_by UUID REFERENCES users(id),
    quality_checked_at TIMESTAMP,

    -- Location and timing
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_name VARCHAR(200),

    -- Status and workflow
    status VARCHAR(20) DEFAULT 'draft', -- draft, pending, approved, completed, cancelled, disputed
    approval_status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP,
    rejection_reason TEXT,

    -- Delivery information
    delivery_date DATE,
    delivery_address TEXT,
    delivery_status VARCHAR(20), -- pending, in_transit, delivered, failed
    delivery_notes TEXT,

    -- Financial tracking
    commission_rate DECIMAL(5, 4), -- Percentage
    commission_amount DECIMAL(15, 2),
    net_amount DECIMAL(15, 2), -- Amount after commission

    -- Audit and sync
    notes TEXT,
    internal_notes TEXT, -- Private notes for staff
    is_synced BOOLEAN DEFAULT false,
    synced_at TIMESTAMP,
    device_id VARCHAR(100), -- Device that created the transaction
    app_version VARCHAR(20),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    sync_version INTEGER DEFAULT 1,

    -- Constraints
    CONSTRAINT transactions_quantity_positive CHECK (quantity > 0),
    CONSTRAINT transactions_unit_price_positive CHECK (unit_price > 0),
    CONSTRAINT transactions_total_positive CHECK (total_amount > 0),
    CONSTRAINT transactions_amount_paid_non_negative CHECK (amount_paid >= 0),
    CONSTRAINT transactions_coordinates_check CHECK (
        (latitude IS NULL AND longitude IS NULL) OR
        (latitude IS NOT NULL AND longitude IS NOT NULL)
    )
);

-- Indexes for performance
CREATE INDEX idx_transactions_number ON transactions(transaction_number);
CREATE INDEX idx_transactions_buyer_id ON transactions(buyer_id);
CREATE INDEX idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX idx_transactions_commodity_id ON transactions(commodity_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_approval_status ON transactions(approval_status);
CREATE INDEX idx_transactions_payment_status ON transactions(payment_status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_location ON transactions USING GIST (point(longitude, latitude));
CREATE INDEX idx_transactions_sync ON transactions(is_synced);
```

#### Supporting Tables

##### File Attachments Table
```sql
CREATE TABLE file_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(50) NOT NULL, -- transaction, customer, commodity, user
    entity_id UUID NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- receipt, photo, document, signature
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    gcs_bucket VARCHAR(100) NOT NULL,
    gcs_path TEXT NOT NULL,
    gcs_url TEXT NOT NULL,

    -- Metadata
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- File processing
    is_processed BOOLEAN DEFAULT false,
    processing_status VARCHAR(20), -- pending, processing, completed, failed
    thumbnail_url TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_file_attachments_entity ON file_attachments(entity_type, entity_id);
CREATE INDEX idx_file_attachments_type ON file_attachments(file_type);
CREATE INDEX idx_file_attachments_uploaded_by ON file_attachments(uploaded_by);
```

##### Audit Logs Table
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[], -- Array of changed field names

    -- Actor information
    user_id UUID REFERENCES users(id),
    user_email VARCHAR(100),
    user_role VARCHAR(50),

    -- Request context
    ip_address INET,
    user_agent TEXT,
    request_id UUID,
    session_id VARCHAR(100),

    -- Timing
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Additional context
    reason TEXT, -- Why the change was made
    notes TEXT
);

CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

##### Sync Metadata Table
```sql
CREATE TABLE sync_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    last_sync_timestamp TIMESTAMP NOT NULL,
    sync_status VARCHAR(20) DEFAULT 'pending', -- pending, in_progress, completed, failed
    records_synced INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    error_message TEXT,
    device_id VARCHAR(100),
    user_id UUID REFERENCES users(id),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(table_name, device_id)
);

CREATE INDEX idx_sync_metadata_table ON sync_metadata(table_name);
CREATE INDEX idx_sync_metadata_device ON sync_metadata(device_id);
CREATE INDEX idx_sync_metadata_status ON sync_metadata(sync_status);
```

## 4. PWA System Components

### 4.1 Authentication & Authorization Service
**Technology**: Firebase Authentication + JavaScript
- **Firebase Authentication** with multiple providers (email, Google, phone)
- **Role-based access control (RBAC)** with Firestore security rules
- **Multi-role support** per user with subcollections
- **Session management** with Firebase Auth tokens
- **Password security** with Firebase's built-in security
- **Account security** with Firebase's fraud protection
- **Password reset** with Firebase Auth email flows

### 4.2 User Management Service
**Technology**: JavaScript + Firestore
- **CRUD operations** for users with client-side validation
- **Role assignment/revocation** with Firestore transactions
- **User profile management** including Cloud Storage uploads
- **Activity logging** with Firestore audit collections
- **User search and filtering** with Firestore queries
- **Real-time user status** with Firestore listeners

### 4.3 Customer Management Service
**Technology**: JavaScript + Firestore + Cloud Storage
- **Customer registration** with client-side validation
- **Unique customer ID generation** with Cloud Functions
- **Contact information management** with Firestore documents
- **Geographic location tracking** with GeoPoint fields
- **Document management** with Cloud Storage integration
- **Customer search** with Firestore composite queries
- **Credit limit management** and payment terms

### 4.4 Commodity Management Service
**Technology**: JavaScript + Firestore + Cloud Storage
- **Commodity catalog management** with Firestore collections
- **Unit of measurement** definitions in reference documents
- **Dynamic pricing** with Firestore real-time updates
- **Quality specifications** with flexible document structure
- **Seasonal availability** tracking with date fields
- **Image gallery management** with Cloud Storage
- **Inventory tracking** with Firestore counters

### 4.5 Assignment Management Service
**Technology**: JavaScript + Firestore
- **Buyer-commodity assignment** with Firestore documents
- **Assignment history tracking** with timestamp queries
- **Bulk assignment operations** with Firestore batch writes
- **Purchase limit management** with document validation
- **Performance tracking** with aggregated queries
- **Assignment validation** with Firestore security rules

### 4.6 Transaction Processing Service
**Technology**: JavaScript + Firestore + Cloud Functions
- **Transaction creation** with offline-first approach
- **Multi-step workflow** using Firestore document states
- **Payment processing** with client-side calculations
- **Quality assessment** integration with photo uploads
- **Receipt generation** with client-side PDF libraries
- **Transaction approval** workflow with Cloud Functions
- **Commission calculation** with Firestore triggers

### 4.7 File Management Service
**Technology**: JavaScript + Firebase Cloud Storage
- **File upload** with client-side validation and compression
- **Image optimization** with Canvas API and compression
- **Secure file access** with Firebase Storage security rules
- **File organization** by collection and document ID
- **Metadata management** with Firestore file documents
- **File cleanup** with Cloud Functions lifecycle management

### 4.8 Offline Synchronization Service
**Technology**: IndexedDB + Service Worker + Firestore
- **Local IndexedDB storage** for offline operations
- **Incremental sync** with Firestore offline persistence
- **Conflict resolution** with Firestore's built-in handling
- **Data versioning** with document timestamps
- **Queue management** for offline transactions
- **Background sync** with Service Worker sync events
- **Sync status tracking** with local storage indicators

### 4.9 Reporting & Analytics Service
**Technology**: JavaScript + Firestore + Firebase Analytics
- **Transaction reports** with Firestore aggregation queries
- **Performance analytics** with Firebase Analytics events
- **Financial reports** with client-side calculations
- **Data export** with JavaScript libraries (jsPDF, xlsx)
- **Dashboard metrics** with real-time Firestore listeners
- **Custom report builder** with dynamic query building

## 5. RESTful API Design

### 5.1 API Architecture Principles
- **RESTful design** with standard HTTP methods
- **Resource-based URLs** with consistent naming
- **JSON request/response** format
- **Stateless authentication** with JWT tokens
- **Versioning** through URL path (/api/v1/)
- **Pagination** for list endpoints
- **Error handling** with standard HTTP status codes
- **Rate limiting** to prevent abuse

### 5.2 Core API Endpoints

#### Authentication Endpoints
```
POST   /api/v1/auth/login              # User login
POST   /api/v1/auth/logout             # User logout
POST   /api/v1/auth/refresh            # Refresh access token
POST   /api/v1/auth/forgot-password    # Request password reset
POST   /api/v1/auth/reset-password     # Reset password with token
GET    /api/v1/auth/me                 # Get current user info
```

#### User Management Endpoints
```
GET    /api/v1/users                   # List users (paginated)
POST   /api/v1/users                   # Create new user
GET    /api/v1/users/:id               # Get user by ID
PUT    /api/v1/users/:id               # Update user
DELETE /api/v1/users/:id               # Deactivate user
GET    /api/v1/users/:id/roles         # Get user roles
POST   /api/v1/users/:id/roles         # Assign role to user
DELETE /api/v1/users/:id/roles/:roleId # Remove role from user
```

#### Customer Management Endpoints
```
GET    /api/v1/customers               # List customers (paginated)
POST   /api/v1/customers               # Create new customer
GET    /api/v1/customers/:id           # Get customer by ID
PUT    /api/v1/customers/:id           # Update customer
DELETE /api/v1/customers/:id           # Deactivate customer
GET    /api/v1/customers/search        # Search customers
POST   /api/v1/customers/:id/files     # Upload customer documents
GET    /api/v1/customers/:id/files     # List customer files
```

#### Commodity Management Endpoints
```
GET    /api/v1/commodities             # List commodities (paginated)
POST   /api/v1/commodities             # Create new commodity
GET    /api/v1/commodities/:id         # Get commodity by ID
PUT    /api/v1/commodities/:id         # Update commodity
DELETE /api/v1/commodities/:id         # Deactivate commodity
GET    /api/v1/commodities/categories  # List commodity categories
POST   /api/v1/commodities/:id/images  # Upload commodity images
```

#### Assignment Management Endpoints
```
GET    /api/v1/assignments             # List assignments (paginated)
POST   /api/v1/assignments             # Create new assignment
GET    /api/v1/assignments/:id         # Get assignment by ID
PUT    /api/v1/assignments/:id         # Update assignment
DELETE /api/v1/assignments/:id         # Deactivate assignment
GET    /api/v1/buyers/:id/commodities  # Get buyer's assigned commodities
```

#### Transaction Management Endpoints
```
GET    /api/v1/transactions            # List transactions (paginated)
POST   /api/v1/transactions            # Create new transaction
GET    /api/v1/transactions/:id        # Get transaction by ID
PUT    /api/v1/transactions/:id        # Update transaction
DELETE /api/v1/transactions/:id        # Cancel transaction
POST   /api/v1/transactions/:id/approve # Approve transaction
POST   /api/v1/transactions/:id/reject  # Reject transaction
GET    /api/v1/transactions/:id/receipt # Generate receipt PDF
POST   /api/v1/transactions/:id/files   # Upload transaction files
```

#### File Management Endpoints
```
POST   /api/v1/files/upload            # Upload file
GET    /api/v1/files/:id               # Get file metadata
DELETE /api/v1/files/:id               # Delete file
GET    /api/v1/files/:id/download      # Download file (signed URL)
```

#### Sync Endpoints
```
GET    /api/v1/sync/status             # Get sync status
POST   /api/v1/sync/pull               # Pull changes from server
POST   /api/v1/sync/push               # Push changes to server
GET    /api/v1/sync/conflicts          # Get sync conflicts
POST   /api/v1/sync/resolve            # Resolve sync conflicts
```

### 5.3 API Response Format
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}
```

### 5.4 Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

## 6. User Workflows

### 5.1 Buyer Workflow
1. **Login** → Authentication
2. **View Dashboard** → Display assigned commodities
3. **Select Commodity** → Choose from assigned list
4. **Select Customer** → Choose or create customer
5. **Enter Transaction Details** → Quantity, price, payment
6. **Confirm Transaction** → Validate and save
7. **Generate Receipt** → Print/share receipt

### 5.2 Admin Workflow
1. **Login** → Authentication
2. **User Management** → Create/edit users and roles
3. **Commodity Management** → Manage commodity catalog
4. **Assignment Management** → Assign commodities to buyers
5. **Reports & Analytics** → View system reports

### 5.3 Supervisor Workflow
1. **Login** → Authentication
2. **Transaction Monitoring** → View buyer transactions
3. **Performance Reports** → Analyze buyer performance
4. **Approval Workflows** → Approve high-value transactions

### 5.4 Evaluator Workflow
1. **Login** → Authentication
2. **Quality Assessment** → Record commodity quality
3. **Price Validation** → Verify transaction prices
4. **Audit Reports** → Generate audit trails

## 6. Offline-First Architecture

### 6.1 Local Storage Strategy
- **SQLite** for structured data
- **Hive** for key-value storage
- **File System** for documents/images

### 6.2 Synchronization Strategy
- **Incremental Sync** → Only sync changed data
- **Conflict Resolution** → Last-write-wins with manual resolution
- **Version Control** → Track data versions for conflict detection
- **Queue System** → Store offline transactions for later sync

### 6.3 Data Consistency
- **Optimistic Locking** → Prevent concurrent edit conflicts
- **Validation Rules** → Ensure data integrity
- **Rollback Mechanism** → Handle failed synchronizations

## 7. Security Considerations

### 7.1 Authentication Security
- Password hashing with bcrypt
- JWT token expiration
- Refresh token rotation
- Account lockout policies

### 7.2 Data Security
- Data encryption at rest
- TLS encryption in transit
- Input validation and sanitization
- SQL injection prevention

### 7.3 Access Control
- Role-based permissions
- Resource-level authorization
- Audit logging
- Session management

## 8. Performance Considerations

### 8.1 Database Optimization
- Proper indexing strategy
- Query optimization
- Connection pooling
- Read replicas for reporting

### 8.2 Caching Strategy
- Redis for session caching
- Application-level caching
- CDN for static assets
- Local caching for offline support

### 8.3 Mobile Performance
- Lazy loading
- Image optimization
- Background sync
- Battery optimization

## 9. Monitoring & Analytics

### 9.1 System Monitoring
- Application performance monitoring
- Database performance metrics
- Error tracking and logging
- Uptime monitoring

### 9.2 Business Analytics
- Transaction volume tracking
- User activity analytics
- Commodity performance metrics
- Revenue reporting

## 10. Deployment Architecture

### 10.1 Environment Strategy
- **Development** → Local development environment
- **Staging** → Pre-production testing
- **Production** → Live system deployment

### 10.2 Infrastructure
- **Load Balancer** → Distribute traffic
- **Application Servers** → Handle business logic
- **Database Cluster** → High availability data storage
- **Backup Systems** → Data protection and recovery

## 11. Google Cloud Storage Implementation

### 11.1 Bucket Structure and Organization
```
dcbuyer-user-files/
├── profiles/
│   └── {userId}/
│       ├── avatar.jpg
│       └── documents/
├── transactions/
│   └── {transactionId}/
│       ├── receipts/
│       ├── photos/
│       └── signatures/
├── commodities/
│   └── {commodityId}/
│       ├── images/
│       └── specifications/
├── customers/
│   └── {customerId}/
│       └── documents/
└── system/
    ├── templates/
    └── backups/
```

### 11.2 File Management Service
```python
# Example Python service for file management
class CloudStorageService:
    def __init__(self):
        self.client = storage.Client()
        self.bucket = self.client.bucket('dcbuyer-user-files')

    def upload_user_file(self, user_id: str, file_type: str, file_data: bytes, filename: str) -> str:
        """Upload user file to appropriate bucket path"""
        blob_name = f"{file_type}/{user_id}/{filename}"
        blob = self.bucket.blob(blob_name)
        blob.upload_from_string(file_data)
        return blob.public_url

    def delete_user_file(self, file_path: str) -> bool:
        """Delete file from storage"""
        blob = self.bucket.blob(file_path)
        blob.delete()
        return True

    def get_signed_url(self, file_path: str, expiration_hours: int = 1) -> str:
        """Generate signed URL for secure file access"""
        blob = self.bucket.blob(file_path)
        return blob.generate_signed_url(
            expiration=datetime.utcnow() + timedelta(hours=expiration_hours)
        )
```

### 11.3 File Security and Access Control
- **IAM Policies**: Role-based access to storage buckets
- **Signed URLs**: Temporary access to private files
- **File Encryption**: Server-side encryption for sensitive documents
- **Access Logging**: Audit trail for file operations
- **Virus Scanning**: Automated malware detection for uploads

## 12. Future Enhancements

### 12.1 Planned Features
- Real-time notifications
- Advanced reporting dashboard
- Mobile payment integration
- GPS tracking for transactions
- Machine learning for price prediction

### 12.2 Scalability Considerations
- Microservices architecture migration
- Horizontal scaling capabilities
- Multi-tenant support
- API rate limiting

---

*This document serves as the foundation for the DCBuyer system development and should be updated as requirements evolve.*
