const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseConnection() {
  try {
    console.log('🔄 Testing database connection...');
    
    // Test 1: Check if users table exists and has data
    console.log('\n📋 Testing users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, is_admin, is_supervisor, is_buyer, status')
      .limit(5);
    
    if (usersError) {
      console.error('❌ Users table error:', usersError.message);
    } else {
      console.log('✅ Users table accessible');
      console.log(`   Found ${users?.length || 0} users`);
      if (users && users.length > 0) {
        console.log('   Sample user:', users[0]);
      }
    }

    // Test 2: Check if buyer_commodity_assignments table exists
    console.log('\n📋 Testing buyer_commodity_assignments table...');
    const { data: assignments, error: assignmentsError } = await supabase
      .from('buyer_commodity_assignments')
      .select('*')
      .limit(5);
    
    if (assignmentsError) {
      console.error('❌ Assignments table error:', assignmentsError.message);
      console.log('   This table might not exist yet. Creating sample data...');
      
      // Try to create the table or insert sample data
      await createSampleData();
    } else {
      console.log('✅ Assignments table accessible');
      console.log(`   Found ${assignments?.length || 0} assignments`);
      if (assignments && assignments.length > 0) {
        console.log('   Sample assignment:', assignments[0]);
      }
    }

    // Test 3: Check if commodities table exists
    console.log('\n📋 Testing commodities table...');
    const { data: commodities, error: commoditiesError } = await supabase
      .from('commodities')
      .select('*')
      .limit(5);
    
    if (commoditiesError) {
      console.error('❌ Commodities table error:', commoditiesError.message);
    } else {
      console.log('✅ Commodities table accessible');
      console.log(`   Found ${commodities?.length || 0} commodities`);
      if (commodities && commodities.length > 0) {
        console.log('   Sample commodity:', commodities[0]);
      }
    }

    console.log('\n🎉 Database connection test completed!');
    
  } catch (error) {
    console.error('❌ Database connection test failed:', error);
  }
}

async function createSampleData() {
  try {
    console.log('🔄 Creating sample data...');
    
    // First, let's check what users exist
    const { data: users } = await supabase
      .from('users')
      .select('id, email, is_buyer')
      .eq('is_buyer', true)
      .limit(1);
    
    if (!users || users.length === 0) {
      console.log('ℹ️  No buyer users found. Sample assignments cannot be created.');
      return;
    }

    const buyerUser = users[0];
    console.log('👤 Found buyer user:', buyerUser.email);

    // Check if commodities table exists and has data
    const { data: commodities } = await supabase
      .from('commodities')
      .select('id, name')
      .limit(3);

    if (!commodities || commodities.length === 0) {
      console.log('ℹ️  No commodities found. Creating sample commodities...');
      
      // Try to insert sample commodities
      const sampleCommodities = [
        { name: 'Rice', unit: 'kg', category: 'grains' },
        { name: 'Wheat', unit: 'kg', category: 'grains' },
        { name: 'Corn', unit: 'kg', category: 'grains' }
      ];

      const { data: newCommodities, error: commodityError } = await supabase
        .from('commodities')
        .insert(sampleCommodities)
        .select();

      if (commodityError) {
        console.error('❌ Failed to create sample commodities:', commodityError.message);
        return;
      }

      console.log('✅ Created sample commodities:', newCommodities?.length);
    }

    console.log('✅ Sample data setup completed');
    
  } catch (error) {
    console.error('❌ Failed to create sample data:', error);
  }
}

// Run the test
testDatabaseConnection();
