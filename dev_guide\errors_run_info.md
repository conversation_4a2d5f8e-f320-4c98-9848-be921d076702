# DCBuyer PWA - Common Errors and Troubleshooting Guide

## Development Environment Setup

### 1. Firebase Configuration Errors
**Error**: Firebase not initialized or configuration missing
```javascript
// Solution: Ensure Firebase config is properly set
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};
```

### 2. Service Worker Registration Issues
**Error**: Service Worker fails to register
```javascript
// Solution: Check HTTPS requirement and file path
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js')
    .then(registration => console.log('SW registered'))
    .catch(error => console.log('SW registration failed'));
}
```

### 3. IndexedDB Access Problems
**Error**: IndexedDB operations fail
```javascript
// Solution: Check browser support and handle errors
if (!window.indexedDB) {
  console.error('IndexedDB not supported');
  // Fallback to localStorage or show error message
}
```

## Runtime Errors

### 4. PWA Installation Issues
**Error**: PWA install prompt doesn't appear
- Ensure HTTPS is enabled (required for PWA)
- Check Web App Manifest is properly configured
- Verify Service Worker is registered and active
- Test with Chrome DevTools > Application > Manifest

### 5. Offline Functionality Problems
**Error**: App doesn't work offline
- Check Service Worker caching strategies
- Verify IndexedDB data persistence
- Test offline scenarios in DevTools > Network > Offline

### 6. Firebase Authentication Errors
**Error**: Auth state persistence issues
```javascript
// Solution: Handle auth state changes properly
firebase.auth().onAuthStateChanged((user) => {
  if (user) {
    // User is signed in
    localStorage.setItem('userLoggedIn', 'true');
  } else {
    // User is signed out
    localStorage.removeItem('userLoggedIn');
  }
});
```

## Performance Issues

### 7. Slow IndexedDB Operations
**Solution**: Use proper indexing and batch operations
```javascript
// Create indexes for frequently queried fields
const transaction = db.transaction(['users'], 'readwrite');
const store = transaction.objectStore('users');
store.createIndex('email', 'email', { unique: true });
```

### 8. Large File Upload Problems
**Solution**: Implement file compression and chunking
```javascript
// Compress images before upload
import imageCompression from 'browser-image-compression';
const compressedFile = await imageCompression(file, {
  maxSizeMB: 1,
  maxWidthOrHeight: 1920
});
```

## Testing and Debugging

### 9. PWA Audit Failures
**Tool**: Use Lighthouse for PWA auditing
- Run Lighthouse in Chrome DevTools
- Address performance, accessibility, and PWA criteria
- Ensure all PWA requirements are met

### 10. Cross-Browser Compatibility
**Issues**: Different behavior across browsers
- Test on Chrome, Firefox, Safari, Edge
- Use feature detection instead of browser detection
- Provide fallbacks for unsupported features

## Deployment Issues

### 11. Firebase Hosting Problems
**Error**: Deployment fails or files not updating
```bash
# Solution: Clear cache and redeploy
firebase hosting:channel:delete preview
firebase deploy --only hosting
```

### 12. HTTPS Certificate Issues
**Error**: PWA features not working due to HTTP
- Ensure Firebase Hosting provides HTTPS
- Test locally with `http-server -S` for HTTPS
- Use ngrok for HTTPS tunneling during development

## Common JavaScript Errors

### 13. Async/Await Issues
```javascript
// Wrong: Not handling promises properly
function getData() {
  return fetch('/api/data').then(response => response.json());
}

// Correct: Proper async/await usage
async function getData() {
  try {
    const response = await fetch('/api/data');
    return await response.json();
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}
```

### 14. Firebase Firestore Security Rules
**Error**: Permission denied errors
```javascript
// Solution: Check Firestore security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Best Practices for Error Prevention

### 15. Error Handling Strategy
```javascript
// Implement global error handling
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  // Log to analytics or error reporting service
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  event.preventDefault();
});
```

### 16. Progressive Enhancement
```javascript
// Check for feature support before using
if ('serviceWorker' in navigator) {
  // Use Service Worker
}

if (window.indexedDB) {
  // Use IndexedDB
} else {
  // Fallback to localStorage
}
```

## Development Tools and Commands

### 17. Useful Chrome DevTools Commands
```javascript
// Clear all storage
localStorage.clear();
sessionStorage.clear();
indexedDB.deleteDatabase('DCBuyerPWA');

// Check Service Worker status
navigator.serviceWorker.getRegistrations().then(registrations => {
  console.log('SW registrations:', registrations);
});

// Test PWA installation
window.addEventListener('beforeinstallprompt', (e) => {
  console.log('PWA install prompt available');
});
```

### 18. Firebase CLI Commands
```bash
# Initialize Firebase project
firebase init

# Deploy to Firebase Hosting
firebase deploy --only hosting

# Run Firebase emulators
firebase emulators:start

# View Firebase logs
firebase functions:log
```

This guide should help troubleshoot common issues when developing the DCBuyer PWA with HTML, CSS, JavaScript, and Firebase.