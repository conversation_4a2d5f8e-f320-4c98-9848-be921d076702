const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function exploreDatabase() {
  try {
    console.log('🔍 Exploring database schema...\n');
    
    // List of potential table names to check
    const tablesToCheck = [
      'users',
      'commodities', 
      'customers',
      'transactions',
      'assignments',
      'buyer_commodity_assignments',
      'roles',
      'user_roles',
      'missions',
      'purchases',
      'orders'
    ];

    for (const tableName of tablesToCheck) {
      await checkTable(tableName);
    }

    console.log('\n🎉 Database exploration completed!');
    
  } catch (error) {
    console.error('❌ Database exploration failed:', error);
  }
}

async function checkTable(tableName) {
  try {
    console.log(`📋 Checking table: ${tableName}`);
    
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error) {
      console.log(`   ❌ Table '${tableName}' not accessible: ${error.message}`);
    } else {
      console.log(`   ✅ Table '${tableName}' exists`);
      
      // Get count
      const { count, error: countError } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (!countError) {
        console.log(`   📊 Records: ${count || 0}`);
      }
      
      // Show sample data structure
      if (data && data.length > 0) {
        console.log(`   🔍 Sample structure:`, Object.keys(data[0]));
        console.log(`   📄 Sample data:`, data[0]);
      } else {
        console.log(`   📄 No data in table`);
      }
    }
    console.log(''); // Empty line for readability
    
  } catch (error) {
    console.log(`   ❌ Error checking table '${tableName}':`, error.message);
    console.log(''); // Empty line for readability
  }
}

// Run the exploration
exploreDatabase();
