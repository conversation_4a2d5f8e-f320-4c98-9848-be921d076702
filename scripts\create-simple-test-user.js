const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createSimpleTestUser() {
  try {
    console.log('🔄 Creating simple test user...');
    
    // Check if user already exists
    const { data: existingUsers, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>');

    if (checkError) {
      console.error('❌ Error checking existing users:', checkError.message);
      return;
    }

    if (existingUsers && existingUsers.length > 0) {
      console.log('✅ Test user already exists:', existingUsers[0]);
      return existingUsers[0];
    }

    // Try different field combinations to find what works
    const testUsers = [
      // Try 1: Basic fields
      {
        email: '<EMAIL>',
        is_admin: false,
        is_supervisor: false,
        is_buyer: true,
        status: 'active'
      },
      // Try 2: With id field
      {
        id: 999,
        email: '<EMAIL>',
        is_admin: false,
        is_supervisor: false,
        is_buyer: true,
        status: 'active'
      },
      // Try 3: Minimal
      {
        email: '<EMAIL>'
      }
    ];

    for (let i = 0; i < testUsers.length; i++) {
      console.log(`\n🔄 Attempt ${i + 1}: Trying user creation...`);
      console.log('   Fields:', Object.keys(testUsers[i]));
      
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert([testUsers[i]])
        .select();

      if (insertError) {
        console.log(`❌ Attempt ${i + 1} failed:`, insertError.message);
      } else {
        console.log(`✅ Attempt ${i + 1} succeeded!`);
        console.log('   Created user:', newUser[0]);
        
        // Now assign the existing mission to this user
        await assignMissionToUser(newUser[0].id);
        return newUser[0];
      }
    }

    console.log('❌ All attempts failed. The users table might have different required fields.');
    
  } catch (error) {
    console.error('❌ Test user creation failed:', error);
  }
}

async function assignMissionToUser(userId) {
  try {
    console.log(`\n🔄 Assigning mission to user ${userId}...`);
    
    // Check existing missions
    const { data: missions, error: missionError } = await supabase
      .from('mission')
      .select('*');

    if (missionError) {
      console.error('❌ Error checking missions:', missionError.message);
      return;
    }

    if (!missions || missions.length === 0) {
      console.log('ℹ️  No missions found to assign');
      return;
    }

    // Assign the first mission to our user
    const { error: updateError } = await supabase
      .from('mission')
      .update({ user_id: userId })
      .eq('id', missions[0].id);

    if (updateError) {
      console.error('❌ Error updating mission assignment:', updateError.message);
    } else {
      console.log('✅ Mission successfully assigned to test user!');
      console.log(`   Mission: ${missions[0].mission_name}`);
      console.log(`   Budget: $${missions[0].budgeted_amount}`);
    }
    
  } catch (error) {
    console.error('❌ Mission assignment failed:', error);
  }
}

// Run the script
createSimpleTestUser().then(() => {
  console.log('\n🎉 Test user setup completed!');
  console.log('\n📱 You can now login with:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123');
});
