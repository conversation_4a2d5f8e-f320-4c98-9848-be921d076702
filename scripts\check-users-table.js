const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkUsersTable() {
  try {
    console.log('🔍 Checking users table structure...');
    
    // Try to get any existing user to see the structure
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Error accessing users table:', error.message);
      return;
    }

    console.log('✅ Users table accessible');
    console.log('📊 Records:', users?.length || 0);

    if (users && users.length > 0) {
      console.log('🔍 User table columns:', Object.keys(users[0]));
      console.log('📄 Sample user:', users[0]);
    } else {
      console.log('📄 No users in table');
      
      // Try to insert a minimal user to see what fields are required
      console.log('\n🔄 Attempting to insert minimal test user...');
      
      const minimalUser = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert([minimalUser])
        .select();

      if (insertError) {
        console.log('❌ Minimal insert failed:', insertError.message);
        console.log('   This tells us about required fields');
        
        // Try with more fields based on common patterns
        console.log('\n🔄 Trying with common user fields...');
        const commonUser = {
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
          role: 'buyer',
          is_admin: false,
          is_supervisor: false,
          is_buyer: true,
          status: 'active'
        };

        const { data: newUser2, error: insertError2 } = await supabase
          .from('users')
          .insert([commonUser])
          .select();

        if (insertError2) {
          console.log('❌ Common fields insert failed:', insertError2.message);
        } else {
          console.log('✅ User created with common fields!');
          console.log('📄 New user:', newUser2);
        }
      } else {
        console.log('✅ Minimal user created!');
        console.log('📄 New user:', newUser);
      }
    }

    console.log('\n🎉 Users table check completed!');
    
  } catch (error) {
    console.error('❌ Users table check failed:', error);
  }
}

// Run the check
checkUsersTable();
