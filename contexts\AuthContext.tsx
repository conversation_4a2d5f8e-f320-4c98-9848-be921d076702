import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';

import { supabase } from '@/lib/supabase';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  isAdmin?: boolean;
  isSupervisor?: boolean;
  isBuyer?: boolean;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const STORAGE_KEY = '@DCBuyer:user';

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load user from storage on app start
  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      console.log('Loading user from storage...');
      const userData = await AsyncStorage.getItem(STORAGE_KEY);
      if (userData) {
        const parsedUser = JSON.parse(userData);
        console.log('User loaded from storage:', parsedUser.email);
        setUser(parsedUser);
      } else {
        console.log('No user found in storage');
      }
    } catch (error) {
      console.error('Error loading user from storage:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Authenticate user against custom users table
   * Author: Noland Gande (<EMAIL>)
   */
  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      console.log('=== CUSTOM TABLE AUTHENTICATION ===');
      console.log('Email:', email);
      console.log('Password provided:', !!password);
      
      // Query the custom users table
      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', email.trim())
        .eq('status', 'active')
        .limit(1);
      
      console.log('Database query result:', { users, error });
      
      if (error) {
        console.error('Database error:', error);
        return { success: false, error: 'Database connection error' };
      }
      
      if (!users || users.length === 0) {
        console.log('User not found in database');
        return { success: false, error: 'Invalid email or password' };
      }
      
      const dbUser = users[0];
      console.log('User found:', {
        id: dbUser.id,
        email: dbUser.email,
        isAdmin: dbUser.is_admin,
        isSupervisor: dbUser.is_supervisor,
        isBuyer: dbUser.is_buyer
      });
      
      // For simplicity, we'll use the default password 'password123' for all users
      // In production, you should hash and store passwords properly
      if (password !== 'password123') {
        console.log('Invalid password provided');
        return { success: false, error: 'Invalid email or password' };
      }
      
      // Create user object
      const userData: User = {
        id: dbUser.id.toString(),
        email: dbUser.email,
        name: dbUser.email.split('@')[0].charAt(0).toUpperCase() + dbUser.email.split('@')[0].slice(1),
        role: dbUser.is_admin ? 'Admin' : 
              dbUser.is_supervisor ? 'Supervisor' : 
              dbUser.is_buyer ? 'Buyer' : 'User',
        isAdmin: dbUser.is_admin,
        isSupervisor: dbUser.is_supervisor,
        isBuyer: dbUser.is_buyer
      };
      
      // Save to storage and state
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(userData));
      setUser(userData);
      
      console.log('=== LOGIN SUCCESS ===');
      console.log('User logged in:', userData);
      
      return { success: true };
      
    } catch (error) {
      console.error('=== LOGIN EXCEPTION ===');
      console.error('Exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      console.log('Logging out user...');
      await AsyncStorage.removeItem(STORAGE_KEY);
      setUser(null);
      console.log('User logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};