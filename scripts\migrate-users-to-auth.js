/**
 * User Migration Script
 * Migrates users from custom 'users' table to Supabase auth.users
 * 
 * Author: <PERSON><PERSON>
 * Website: www.dakoiims.com
 * Email: <EMAIL>
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseServiceKey = 'YOUR_SERVICE_ROLE_KEY_HERE'; // ⚠️ REPLACE THIS with your service_role key from Supabase dashboard

console.log('⚠️  IMPORTANT: You need to replace the SERVICE_ROLE_KEY above with your actual service role key from Supabase dashboard');
console.log('📍 Get it from: Supabase Dashboard > Settings > API > service_role key (secret)');
console.log('');

// Create Supabase client with service role (has admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Default password for migrated users (they should change this on first login)
const DEFAULT_PASSWORD = 'password123';

async function migrateUsers() {
  try {
    console.log('🔄 Starting user migration from users table to auth.users...');
    
    // Fetch all users from the custom users table
    const { data: customUsers, error: fetchError } = await supabase
      .from('users')
      .select('*');

    if (fetchError) {
      console.error('❌ Error fetching users from custom table:', fetchError);
      return;
    }

    if (!customUsers || customUsers.length === 0) {
      console.log('ℹ️  No users found in custom users table');
      return;
    }

    console.log(`📊 Found ${customUsers.length} users to migrate:`);
    customUsers.forEach(user => {
      console.log(`   - ${user.email} (ID: ${user.id})`);
    });
    console.log('');

    // Migrate each user
    const results = [];
    for (const user of customUsers) {
      console.log(`🔄 Migrating user: ${user.email}`);
      
      try {
        // Create user in auth.users using admin API
        const { data: authUser, error: createError } = await supabase.auth.admin.createUser({
          email: user.email,
          password: DEFAULT_PASSWORD,
          email_confirm: true, // Auto-confirm email
          user_metadata: {
            name: user.email.split('@')[0], // Use email prefix as name
            role: user.is_admin ? 'Admin' : 
                  user.is_supervisor ? 'Supervisor' : 
                  user.is_buyer ? 'Buyer' : 'User',
            migrated_from_custom_table: true,
            original_id: user.id
          }
        });

        if (createError) {
          console.error(`   ❌ Failed to create auth user for ${user.email}:`, createError.message);
          results.push({ email: user.email, success: false, error: createError.message });
        } else {
          console.log(`   ✅ Successfully created auth user for ${user.email}`);
          console.log(`      Auth ID: ${authUser.user.id}`);
          results.push({ 
            email: user.email, 
            success: true, 
            authId: authUser.user.id,
            originalId: user.id 
          });
        }
      } catch (error) {
        console.error(`   ❌ Exception creating auth user for ${user.email}:`, error.message);
        results.push({ email: user.email, success: false, error: error.message });
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Summary
    console.log('\n📋 MIGRATION SUMMARY:');
    console.log('='.repeat(50));
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ Successfully migrated: ${successful.length} users`);
    if (successful.length > 0) {
      successful.forEach(result => {
        console.log(`   - ${result.email} → Auth ID: ${result.authId}`);
      });
    }
    
    if (failed.length > 0) {
      console.log(`❌ Failed to migrate: ${failed.length} users`);
      failed.forEach(result => {
        console.log(`   - ${result.email}: ${result.error}`);
      });
    }

    console.log('\n🔐 IMPORTANT NOTES:');
    console.log('- All migrated users have the default password: "password123"');
    console.log('- Users should change their password on first login');
    console.log('- Email addresses are auto-confirmed');
    console.log('- User roles are preserved in user_metadata');
    console.log('- Original user IDs are stored in user_metadata.original_id');
    
  } catch (error) {
    console.error('❌ Migration failed with error:', error);
  }
}

// Run the migration
if (require.main === module) {
  migrateUsers()
    .then(() => {
      console.log('\n🎉 Migration process completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration process failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateUsers };