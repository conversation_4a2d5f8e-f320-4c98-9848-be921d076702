const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function findMissionsData() {
  try {
    console.log('🔍 Looking for missions data...\n');
    
    // Check more table variations
    const missionTableVariations = [
      'missions',
      'mission',
      'workplan_missions',
      'workplan_mission',
      'buyer_missions',
      'commodity_missions',
      'purchase_missions',
      'assignments',
      'mission_assignments',
      'buyer_assignments'
    ];

    console.log('📋 Checking mission table variations...');
    for (const tableName of missionTableVariations) {
      await checkTable(tableName);
    }

    // Since transactions have mission_id, let's see what mission IDs exist
    console.log('🔍 Analyzing mission_id values in transactions...');
    const { data: transactions, error } = await supabase
      .from('transactions')
      .select('mission_id, user_id, commodity_id, quantity, payment_amount, transaction_date')
      .order('mission_id');

    if (error) {
      console.error('❌ Error fetching transactions:', error.message);
    } else {
      console.log('✅ Found transactions with mission_id:');
      
      // Group by mission_id
      const missionGroups = {};
      transactions.forEach(tx => {
        if (!missionGroups[tx.mission_id]) {
          missionGroups[tx.mission_id] = [];
        }
        missionGroups[tx.mission_id].push(tx);
      });

      Object.keys(missionGroups).forEach(missionId => {
        const txs = missionGroups[missionId];
        const totalAmount = txs.reduce((sum, tx) => sum + (tx.payment_amount || 0), 0);
        const totalQuantity = txs.reduce((sum, tx) => sum + (tx.quantity || 0), 0);
        
        console.log(`   Mission ID ${missionId}:`);
        console.log(`     - ${txs.length} transactions`);
        console.log(`     - Total amount: $${totalAmount}`);
        console.log(`     - Total quantity: ${totalQuantity}`);
        console.log(`     - Users: ${[...new Set(txs.map(tx => tx.user_id))].join(', ')}`);
        console.log(`     - Commodities: ${[...new Set(txs.map(tx => tx.commodity_id))].join(', ')}`);
        console.log('');
      });
    }

    // Let's also check if there are any other tables we missed
    console.log('🔍 Checking for other potential tables...');
    const otherTables = [
      'locations',
      'location',
      'workplan',
      'workplans',
      'workplan_report',
      'reports',
      'budgets',
      'budget',
      'purchase_orders',
      'orders'
    ];

    for (const tableName of otherTables) {
      await checkTable(tableName);
    }

    console.log('\n🎉 Mission data exploration completed!');
    
  } catch (error) {
    console.error('❌ Mission data exploration failed:', error);
  }
}

async function checkTable(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error) {
      // Don't log errors for missing tables to reduce noise
      return;
    } else {
      console.log(`   ✅ Found table: ${tableName}`);
      
      // Get count
      const { count, error: countError } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (!countError) {
        console.log(`      📊 Records: ${count || 0}`);
      }
      
      // Show sample data structure
      if (data && data.length > 0) {
        console.log(`      🔍 Columns:`, Object.keys(data[0]));
        console.log(`      📄 Sample:`, data[0]);
      }
      console.log('');
    }
    
  } catch (error) {
    // Ignore errors for missing tables
  }
}

// Run the exploration
findMissionsData();
