import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { ThemedText } from '@/components/themed-text';
import { ThemedView } from '@/components/themed-view';
import { IconSymbol } from '@/components/ui/icon-symbol';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/use-color-scheme';

export default function DashboardScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { user, logout, isAuthenticated } = useAuth();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/');
    }
  }, [isAuthenticated, router]);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/');
          },
        },
      ]
    );
  };

  // Don't render if not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  // Mock data for demonstration
  const userStats = {
    totalTransactions: 156,
    activeCommodities: 8,
    monthlyVolume: '2.4M',
    pendingOrders: 12
  };

  const recentTransactions = [
    { id: 1, commodity: 'Rice', amount: '500 kg', value: '$1,250', date: 'Today' },
    { id: 2, commodity: 'Wheat', amount: '300 kg', value: '$890', date: 'Yesterday' },
    { id: 3, commodity: 'Corn', amount: '750 kg', value: '$1,680', date: '2 days ago' },
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header Section */}
      <LinearGradient
        colors={isDark ? ['#0f172a', '#1e293b'] : ['#1e3a5f', '#22c55e']}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <Image
              source={require('@/assets/images/dcb-logo.png')}
              style={styles.logo}
              contentFit="contain"
            />
          </View>
          <View style={styles.welcomeSection}>
            <View style={styles.welcomeTextContainer}>
              <ThemedText style={styles.welcomeText}>Welcome back!</ThemedText>
              <ThemedText style={styles.userNameText}>{user.name}</ThemedText>
              <ThemedText style={styles.roleText}>{user.role}</ThemedText>
            </View>
            <TouchableOpacity 
              style={styles.logoutButton}
              onPress={handleLogout}
            >
              <Icon name="logout" size={24} color="#ffffff" />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
      {/* Stats Cards */}
      <ThemedView style={styles.statsContainer}>
        <ThemedText style={styles.sectionTitle}>Overview</ThemedText>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Icon name="trending-up" size={24} color="#22c55e" />
            <ThemedText style={styles.statNumber}>{userStats.totalTransactions}</ThemedText>
            <ThemedText style={styles.statLabel}>Total Transactions</ThemedText>
          </View>
          <View style={styles.statCard}>
            <Icon name="eco" size={24} color="#22c55e" />
            <ThemedText style={styles.statNumber}>{userStats.activeCommodities}</ThemedText>
            <ThemedText style={styles.statLabel}>Active Commodities</ThemedText>
          </View>
          <View style={styles.statCard}>
            <Icon name="attach-money" size={24} color="#22c55e" />
            <ThemedText style={styles.statNumber}>{userStats.monthlyVolume}</ThemedText>
            <ThemedText style={styles.statLabel}>Monthly Volume</ThemedText>
          </View>
          <View style={styles.statCard}>
            <Icon name="schedule" size={24} color="#f59e0b" />
            <ThemedText style={styles.statNumber}>{userStats.pendingOrders}</ThemedText>
            <ThemedText style={styles.statLabel}>Pending Orders</ThemedText>
          </View>
        </View>
      </ThemedView>

      {/* Quick Actions */}
      <ThemedView style={styles.actionsContainer}>
        <ThemedText style={styles.sectionTitle}>Quick Actions</ThemedText>
        <View style={styles.actionsGrid}>
          <TouchableOpacity style={styles.actionButton}>
            <LinearGradient
              colors={['#22c55e', '#16a34a']}
              style={styles.actionButtonGradient}
            >
              <Icon name="add-shopping-cart" size={28} color="#ffffff" />
              <ThemedText style={styles.actionButtonText}>New Purchase</ThemedText>
            </LinearGradient>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <LinearGradient
              colors={['#1e3a5f', '#334155']}
              style={styles.actionButtonGradient}
            >
              <Icon name="inventory" size={28} color="#ffffff" />
              <ThemedText style={styles.actionButtonText}>View Commodities</ThemedText>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ThemedView>
      {/* Recent Transactions */}
      <ThemedView style={styles.transactionsContainer}>
        <View style={styles.sectionHeader}>
          <ThemedText style={styles.sectionTitle}>Recent Transactions</ThemedText>
          <TouchableOpacity>
            <ThemedText style={styles.viewAllText}>View All</ThemedText>
          </TouchableOpacity>
        </View>
        <View style={styles.transactionsList}>
          {recentTransactions.map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionIcon}>
                <IconSymbol name="leaf.fill" size={20} color="#22c55e" />
              </View>
              <View style={styles.transactionDetails}>
                <ThemedText style={styles.transactionCommodity}>{transaction.commodity}</ThemedText>
                <ThemedText style={styles.transactionAmount}>{transaction.amount}</ThemedText>
              </View>
              <View style={styles.transactionValue}>
                <ThemedText style={styles.transactionPrice}>{transaction.value}</ThemedText>
                <ThemedText style={styles.transactionDate}>{transaction.date}</ThemedText>
              </View>
            </View>
          ))}
        </View>
      </ThemedView>

      {/* Navigation Cards */}
      <ThemedView style={styles.navigationContainer}>
        <ThemedText style={styles.sectionTitle}>Explore</ThemedText>
        <View style={styles.navigationGrid}>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="inventory" size={32} color="#1e3a5f" />
            <ThemedText style={styles.navCardTitle}>Commodities</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>Browse available commodities</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="bar-chart" size={32} color="#1e3a5f" />
            <ThemedText style={styles.navCardTitle}>Reports</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>View analytics & insights</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="settings" size={32} color="#1e3a5f" />
            <ThemedText style={styles.navCardTitle}>Settings</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>Configure your preferences</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="help" size={32} color="#1e3a5f" />
            <ThemedText style={styles.navCardTitle}>Support</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>Get help & assistance</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  logoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  logo: {
    width: 50,
    height: 50,
  },
  welcomeSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  welcomeTextContainer: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.9,
  },
  userNameText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginTop: 4,
  },
  roleText: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.8,
    marginTop: 2,
  },
  logoutButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 12,
    marginLeft: 16,
  },
  statsContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e3a5f',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e3a5f',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 4,
  },
  actionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  actionButtonGradient: {
    paddingVertical: 20,
    paddingHorizontal: 16,
    borderRadius: 16,
    alignItems: 'center',
    gap: 8,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  transactionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#22c55e',
    fontWeight: '600',
  },
  transactionsList: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0fdf4',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionCommodity: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e3a5f',
  },
  transactionAmount: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 2,
  },
  transactionValue: {
    alignItems: 'flex-end',
  },
  transactionPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#22c55e',
  },
  transactionDate: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 2,
  },
  navigationContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  navigationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  navCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  navCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e3a5f',
    marginTop: 12,
    textAlign: 'center',
  },
  navCardSubtitle: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 4,
  },
});
