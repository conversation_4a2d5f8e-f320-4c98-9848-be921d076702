import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { ThemedText } from '@/components/themed-text';
import { ThemedView } from '@/components/themed-view';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/use-color-scheme';
import { MissionsService, type MissionWithDetails } from '@/services/MissionsService';

export default function DashboardScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { user, logout, isAuthenticated } = useAuth();
  const router = useRouter();

  // State for missions data
  const [missions, setMissions] = useState<MissionWithDetails[]>([]);
  const [isLoadingMissions, setIsLoadingMissions] = useState(false);
  const [missionsError, setMissionsError] = useState<string | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/');
    }
  }, [isAuthenticated, router]);

  // Fetch missions when component loads (for testing)
  useEffect(() => {
    // For testing, always fetch missions regardless of authentication
    fetchMissions();
  }, []);

  const fetchMissions = async () => {
    // For testing purposes, use hardcoded user ID 2 since we know there's a mission assigned to it
    // In production, this would use user?.id
    const testUserId = '2';

    setIsLoadingMissions(true);
    setMissionsError(null);

    try {
      console.log('🔄 Fetching missions for test user ID:', testUserId);
      const result = await MissionsService.getAssignedMissions(testUserId);

      if (result.success && result.data) {
        setMissions(result.data);
        console.log('✅ Missions loaded successfully:', result.data.length);
        console.log('📄 Mission data:', result.data);
      } else {
        setMissionsError(result.error || 'Failed to load missions');
        console.error('❌ Failed to load missions:', result.error);
      }
    } catch (error) {
      setMissionsError('An unexpected error occurred');
      console.error('❌ Exception while fetching missions:', error);
    } finally {
      setIsLoadingMissions(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/');
          },
        },
      ]
    );
  };

  // Don't render if not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  // Real-time data from database
  const userStats = {
    assignedMissions: missions.length,
    totalTransactions: missions.length, // We'll need to fetch actual transaction count separately
    activeCustomers: 23, // This would come from a separate API call
    syncStatus: isLoadingMissions ? 'Syncing...' : 'Online'
  };

  const recentTransactions = [
    { id: 1, mission: 'Rice Purchase - Port Moresby', commodity: 'Rice', amount: '500 kg', value: '$1,250', date: 'Today', status: 'synced' },
    { id: 2, mission: 'Wheat Purchase - Lae', commodity: 'Wheat', amount: '300 kg', value: '$890', date: 'Yesterday', status: 'synced' },
    { id: 3, mission: 'Corn Purchase - Mt. Hagen', commodity: 'Corn', amount: '750 kg', value: '$1,680', date: '2 days ago', status: 'pending' },
  ];

  // Use real missions data from database
  const assignedMissions = missions.map(mission => {
    const budgetUsage = MissionsService.calculateBudgetUsage(mission);
    const missionStatus = MissionsService.getMissionStatus(mission);

    return {
      id: mission.id,
      title: MissionsService.getMissionTitle(mission),
      commodity: mission.commodity?.commodity_name || 'Unknown',
      budget: MissionsService.formatCurrency(mission.budgeted_amount),
      used: MissionsService.formatCurrency(mission.actual_amount),
      location: mission.location?.location_name || 'Unknown Location',
      status: missionStatus.status,
      budgetPercentage: budgetUsage.percentage,
      budgetStatus: budgetUsage.status
    };
  });

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header Section */}
      <LinearGradient
        colors={isDark ? ['#0f172a', '#1e293b'] : ['#1e3a5f', '#22c55e']}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <Image
              source={require('@/assets/images/dcb-logo.png')}
              style={styles.logo}
              contentFit="contain"
            />
          </View>
          <View style={styles.welcomeSection}>
            <View style={styles.welcomeTextContainer}>
              <ThemedText style={styles.welcomeText}>Welcome back!</ThemedText>
              <ThemedText style={styles.userNameText}>{user.name}</ThemedText>
              <ThemedText style={styles.roleText}>{user.role}</ThemedText>
            </View>
            <TouchableOpacity 
              style={styles.logoutButton}
              onPress={handleLogout}
            >
              <Icon name="logout" size={24} color="#ffffff" />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
      {/* Stats Cards - Core Features Overview */}
      <ThemedView style={styles.statsContainer}>
        <View style={styles.sectionHeader}>
          <ThemedText style={styles.sectionTitle}>Dashboard Overview</ThemedText>
          <View style={styles.syncIndicator}>
            <Icon name="cloud-done" size={16} color="#22c55e" />
            <ThemedText style={styles.syncText}>{userStats.syncStatus}</ThemedText>
          </View>
        </View>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Icon name="assignment" size={24} color="#3b82f6" />
            <ThemedText style={styles.statNumber}>{userStats.assignedMissions}</ThemedText>
            <ThemedText style={styles.statLabel}>Assigned Missions</ThemedText>
          </View>
          <View style={styles.statCard}>
            <Icon name="receipt" size={24} color="#22c55e" />
            <ThemedText style={styles.statNumber}>{userStats.totalTransactions}</ThemedText>
            <ThemedText style={styles.statLabel}>Total Transactions</ThemedText>
          </View>
          <View style={styles.statCard}>
            <Icon name="people" size={24} color="#f59e0b" />
            <ThemedText style={styles.statNumber}>{userStats.activeCustomers}</ThemedText>
            <ThemedText style={styles.statLabel}>Active Customers</ThemedText>
          </View>
          <View style={styles.statCard}>
            <Icon name="offline-bolt" size={24} color="#8b5cf6" />
            <ThemedText style={styles.statNumber}>Ready</ThemedText>
            <ThemedText style={styles.statLabel}>Offline Mode</ThemedText>
          </View>
        </View>
      </ThemedView>

      {/* Core Features - Buy, Transactions, Customers */}
      <ThemedView style={styles.actionsContainer}>
        <ThemedText style={styles.sectionTitle}>Core Features</ThemedText>
        <View style={styles.coreFeatureGrid}>
          <TouchableOpacity style={styles.featureCard}>
            <LinearGradient
              colors={['#3b82f6', '#2563eb']}
              style={styles.featureCardGradient}
            >
              <Icon name="shopping-cart" size={32} color="#ffffff" />
              <ThemedText style={styles.featureCardTitle}>BUY</ThemedText>
              <ThemedText style={styles.featureCardSubtitle}>Mission-based purchasing with offline support</ThemedText>
            </LinearGradient>
          </TouchableOpacity>
          <TouchableOpacity style={styles.featureCard}>
            <LinearGradient
              colors={['#22c55e', '#16a34a']}
              style={styles.featureCardGradient}
            >
              <Icon name="receipt-long" size={32} color="#ffffff" />
              <ThemedText style={styles.featureCardTitle}>TRANSACTIONS</ThemedText>
              <ThemedText style={styles.featureCardSubtitle}>Offline transaction management & sync</ThemedText>
            </LinearGradient>
          </TouchableOpacity>
          <TouchableOpacity style={styles.featureCard}>
            <LinearGradient
              colors={['#f59e0b', '#d97706']}
              style={styles.featureCardGradient}
            >
              <Icon name="people" size={32} color="#ffffff" />
              <ThemedText style={styles.featureCardTitle}>CUSTOMERS</ThemedText>
              <ThemedText style={styles.featureCardSubtitle}>Customer relationship management</ThemedText>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ThemedView>
      {/* Assigned Missions */}
      <ThemedView style={styles.missionsContainer}>
        <View style={styles.sectionHeader}>
          <ThemedText style={styles.sectionTitle}>My Assigned Missions</ThemedText>
          <TouchableOpacity onPress={fetchMissions}>
            <ThemedText style={styles.viewAllText}>
              {isLoadingMissions ? 'Refreshing...' : 'Refresh'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        {isLoadingMissions ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#3b82f6" />
            <ThemedText style={styles.loadingText}>Loading missions...</ThemedText>
          </View>
        ) : missionsError ? (
          <View style={styles.errorContainer}>
            <Icon name="error-outline" size={24} color="#ef4444" />
            <ThemedText style={styles.errorText}>{missionsError}</ThemedText>
            <TouchableOpacity style={styles.retryButton} onPress={fetchMissions}>
              <ThemedText style={styles.retryText}>Retry</ThemedText>
            </TouchableOpacity>
          </View>
        ) : assignedMissions.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Icon name="assignment" size={48} color="#9ca3af" />
            <ThemedText style={styles.emptyTitle}>No Missions Assigned</ThemedText>
            <ThemedText style={styles.emptyText}>Contact your administrator to get assigned to missions</ThemedText>
          </View>
        ) : (
          <View style={styles.missionsList}>
            {assignedMissions.map((mission) => (
              <View key={mission.id} style={styles.missionItem}>
                <View style={styles.missionIcon}>
                  <Icon name="assignment" size={20} color="#3b82f6" />
                </View>
                <View style={styles.missionDetails}>
                  <ThemedText style={styles.missionTitle}>{mission.title}</ThemedText>
                  <ThemedText style={styles.missionLocation}>{mission.location} • {mission.commodity}</ThemedText>
                  <View style={styles.budgetProgress}>
                    <ThemedText style={styles.budgetText}>Budget: {mission.used} / {mission.budget}</ThemedText>
                    <View style={styles.progressBar}>
                      <View style={[
                        styles.progressFill,
                        {
                          width: `${Math.min(mission.budgetPercentage, 100)}%`,
                          backgroundColor: mission.budgetStatus === 'exceeded' ? '#ef4444' :
                                         mission.budgetStatus === 'high' ? '#f59e0b' : '#22c55e'
                        }
                      ]} />
                    </View>
                  </View>
                </View>
                <View style={styles.missionStatus}>
                  <Icon
                    name="circle"
                    size={8}
                    color={mission.status === 'active' ? '#22c55e' : '#64748b'}
                  />
                  <ThemedText style={[
                    styles.statusText,
                    { color: mission.status === 'active' ? '#22c55e' : '#64748b' }
                  ]}>
                    {mission.status}
                  </ThemedText>
                </View>
              </View>
            ))}
          </View>
        )}
      </ThemedView>

      {/* Recent Transactions */}
      <ThemedView style={styles.transactionsContainer}>
        <View style={styles.sectionHeader}>
          <ThemedText style={styles.sectionTitle}>Recent Transactions</ThemedText>
          <TouchableOpacity>
            <ThemedText style={styles.viewAllText}>View All</ThemedText>
          </TouchableOpacity>
        </View>
        <View style={styles.transactionsList}>
          {recentTransactions.map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionIcon}>
                <Icon name="receipt" size={20} color="#22c55e" />
              </View>
              <View style={styles.transactionDetails}>
                <ThemedText style={styles.transactionCommodity}>{transaction.commodity}</ThemedText>
                <ThemedText style={styles.transactionMission}>{transaction.mission}</ThemedText>
                <ThemedText style={styles.transactionAmount}>{transaction.amount}</ThemedText>
              </View>
              <View style={styles.transactionValue}>
                <ThemedText style={styles.transactionPrice}>{transaction.value}</ThemedText>
                <ThemedText style={styles.transactionDate}>{transaction.date}</ThemedText>
                <View style={styles.syncStatus}>
                  <Icon
                    name={transaction.status === 'synced' ? 'cloud-done' : 'cloud-upload'}
                    size={12}
                    color={transaction.status === 'synced' ? '#22c55e' : '#f59e0b'}
                  />
                  <ThemedText style={styles.syncStatusText}>{transaction.status}</ThemedText>
                </View>
              </View>
            </View>
          ))}
        </View>
      </ThemedView>

      {/* Offline-First Features */}
      <ThemedView style={styles.navigationContainer}>
        <ThemedText style={styles.sectionTitle}>Offline-First Capabilities</ThemedText>
        <View style={styles.navigationGrid}>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="cloud-off" size={32} color="#8b5cf6" />
            <ThemedText style={styles.navCardTitle}>Offline Mode</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>Work without internet connection</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="sync" size={32} color="#22c55e" />
            <ThemedText style={styles.navCardTitle}>Data Sync</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>Automatic background synchronization</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="storage" size={32} color="#3b82f6" />
            <ThemedText style={styles.navCardTitle}>Local Storage</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>SQLite + AsyncStorage</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navCard}>
            <Icon name="security" size={32} color="#f59e0b" />
            <ThemedText style={styles.navCardTitle}>Data Security</ThemedText>
            <ThemedText style={styles.navCardSubtitle}>Encrypted local data storage</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  logoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  logo: {
    width: 50,
    height: 50,
  },
  welcomeSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  welcomeTextContainer: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.9,
  },
  userNameText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginTop: 4,
  },
  roleText: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.8,
    marginTop: 2,
  },
  logoutButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 12,
    marginLeft: 16,
  },
  statsContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e3a5f',
    marginBottom: 16,
  },
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  syncText: {
    fontSize: 12,
    color: '#22c55e',
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e3a5f',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 4,
  },
  actionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  coreFeatureGrid: {
    gap: 16,
  },
  featureCard: {
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 8,
  },
  featureCardGradient: {
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 20,
    alignItems: 'center',
    gap: 8,
  },
  featureCardTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  featureCardSubtitle: {
    color: '#ffffff',
    fontSize: 12,
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 4,
  },
  missionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  missionsList: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  missionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  missionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#eff6ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  missionDetails: {
    flex: 1,
  },
  missionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e3a5f',
    marginBottom: 4,
  },
  missionLocation: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 8,
  },
  budgetProgress: {
    gap: 4,
  },
  budgetText: {
    fontSize: 12,
    color: '#64748b',
  },
  progressBar: {
    height: 4,
    backgroundColor: '#e2e8f0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#22c55e',
    borderRadius: 2,
  },
  missionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 12,
    color: '#22c55e',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  transactionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#22c55e',
    fontWeight: '600',
  },
  transactionsList: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0fdf4',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionCommodity: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e3a5f',
  },
  transactionMission: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 2,
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 4,
  },
  syncStatusText: {
    fontSize: 10,
    color: '#64748b',
    textTransform: 'capitalize',
  },
  transactionValue: {
    alignItems: 'flex-end',
  },
  transactionPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#22c55e',
  },
  transactionDate: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 2,
  },
  navigationContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  navigationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  navCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  navCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e3a5f',
    marginTop: 12,
    textAlign: 'center',
  },
  navCardSubtitle: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 4,
  },
  // Loading and error states
  loadingContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  loadingText: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 12,
  },
  errorContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  errorText: {
    fontSize: 14,
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e3a5f',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
  },
});
