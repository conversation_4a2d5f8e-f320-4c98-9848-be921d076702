import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM';

export const supabase = createClient(supabaseUrl, supabaseKey);

// Export the URL and key for reference if needed
export { supabaseKey, supabaseUrl };
