const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTestUser() {
  try {
    console.log('🔄 Creating test user...');
    
    // Check if user already exists
    const { data: existingUsers, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>');

    if (checkError) {
      console.error('❌ Error checking existing users:', checkError.message);
      return;
    }

    if (existingUsers && existingUsers.length > 0) {
      console.log('✅ Test user already exists:', existingUsers[0]);
      console.log('   User ID:', existingUsers[0].id);
      console.log('   Email:', existingUsers[0].email);
      console.log('   Is Buyer:', existingUsers[0].is_buyer);
      return;
    }

    // Create test user
    const testUser = {
      email: '<EMAIL>',
      password: 'password123', // Simple password for testing
      first_name: 'Test',
      last_name: 'Buyer',
      phone: '************',
      is_buyer: true,
      is_admin: false,
      is_supervisor: false,
      status: 'active'
    };

    const { data: newUser, error: insertError } = await supabase
      .from('users')
      .insert([testUser])
      .select()
      .single();

    if (insertError) {
      console.error('❌ Error creating test user:', insertError.message);
      return;
    }

    console.log('✅ Test user created successfully!');
    console.log('   User ID:', newUser.id);
    console.log('   Email:', newUser.email);
    console.log('   Password: password123');
    console.log('   Is Buyer:', newUser.is_buyer);

    // Now check if there's a mission assigned to user_id 2 (from our earlier exploration)
    console.log('\n🔍 Checking existing missions...');
    const { data: missions, error: missionError } = await supabase
      .from('mission')
      .select('*')
      .eq('user_id', 2);

    if (missionError) {
      console.error('❌ Error checking missions:', missionError.message);
    } else if (missions && missions.length > 0) {
      console.log('✅ Found existing missions for user_id 2:');
      missions.forEach(mission => {
        console.log(`   - Mission: ${mission.mission_name}`);
        console.log(`     Status: ${mission.mission_status}`);
        console.log(`     Budget: $${mission.budgeted_amount}`);
        console.log(`     Actual: $${mission.actual_amount}`);
      });

      // Update the mission to be assigned to our new test user
      if (newUser.id) {
        console.log(`\n🔄 Assigning mission to new test user (ID: ${newUser.id})...`);
        const { error: updateError } = await supabase
          .from('mission')
          .update({ user_id: newUser.id })
          .eq('id', missions[0].id);

        if (updateError) {
          console.error('❌ Error updating mission assignment:', updateError.message);
        } else {
          console.log('✅ Mission successfully assigned to test user!');
        }
      }
    } else {
      console.log('ℹ️  No missions found to assign');
    }

    console.log('\n🎉 Test user setup completed!');
    console.log('\n📱 You can now login with:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    
  } catch (error) {
    console.error('❌ Test user creation failed:', error);
  }
}

// Run the script
createTestUser();
