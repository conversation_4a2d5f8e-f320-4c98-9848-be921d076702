# DCBuyer Mobile Specifications - Core Features User Stories

## 📋 Executive Summary

This document provides detailed user stories and current system features for the three core functionalities of the DCBuyer system: **Buy**, **Transactions**, and **Customers**. These features form the foundation of the commodity trading and purchasing management platform with **offline-first capabilities**.

**System Architecture**: React Native + Expo with Offline-First Architecture
**Backend**: Supabase PostgreSQL with real-time sync
**Local Storage**: AsyncStorage + SQLite + WatermelonDB
**Current Status**: Mobile application with offline-first CRUD operations
**Target Platform**: Cross-platform mobile app (iOS, Android, Web)

---

## 🛒 BUY FEATURE

### Current System Implementation

**Status**: ✅ Offline-First Mobile Implementation
**Components**: `BuyScreen.tsx`, `MissionSelector.tsx`, `TransactionForm.tsx`
**Contexts**: `AuthContext.tsx`, `DataContext.tsx`, `SyncContext.tsx`
**Local Storage**: AsyncStorage + SQLite for offline missions and transactions
**Sync**: Real-time sync with Supa<PERSON> when online

### System Architecture

The Buy feature operates through a mission-based authorization system where:
- Administrators assign buyers to specific missions
- Each mission contains commodity, location, and budget information
- Buyers can only create transactions for their assigned missions
- All transactions are validated against mission parameters

### Current Capabilities

#### Offline-First Mobile Interface (✅ Implemented)
- **Offline Mission Management**: Download and cache assigned missions locally
- **Offline Transaction Creation**: Create transactions without internet connection
- **Local Budget Tracking**: Real-time budget calculations stored locally
- **Offline Authorization**: Validate buyer permissions using cached mission data
- **Local Transaction Validation**: Ensure commodity and location match using local data
- **Background Sync**: Automatic sync when network becomes available
- **Conflict Resolution**: Handle sync conflicts with server data

#### Mobile Buyer Interface Features
- **Touch-Optimized Mission Dashboard**: Swipe-friendly mission cards with offline indicators
- **Offline Buy Interface**: Streamlined transaction creation with local validation
- **Local Transaction History**: Instant access to transaction history without network
- **Offline Search**: Search missions and transactions using local database
- **Network Status Indicators**: Clear visual feedback on sync status

### Detailed User Stories

#### Epic: Mission-Based Purchasing System

**As an Administrator:**
- I want to assign buyers to specific missions, so that purchasing responsibilities are clearly defined
- I want to set commodity and location constraints for missions, so that buyers purchase the right products in the right places
- I want to set budget limits for missions, so that spending is controlled
- I want to monitor all transactions across all missions, so that I can oversee purchasing activities
- I want to validate that buyers only create transactions for their assigned missions, so that authorization is enforced
- I want to see real-time budget tracking, so that I can prevent overspending

**As a Buyer:**
- I want to view my assigned missions offline, so that I can work without internet connection
- I want to see mission details including commodity, location, and budget stored locally, so that I can plan purchases anywhere
- I want to create transactions for my assigned missions offline, so that I can record purchases in remote areas
- I want to see my remaining budget calculated locally in real-time, so that I don't overspend
- I want to view my transaction history instantly from local storage, so that I can track activities without waiting for network
- I want to receive immediate validation feedback using local data, so that I can correct errors instantly
- I want to see sync status indicators, so that I know when my data will be uploaded to the server
- I want automatic background sync when network is available, so that my data stays up-to-date without manual intervention

#### Epic: Transaction Creation Workflow

**As a Buyer:**
- I want to select from my assigned missions, so that I can choose which mission to purchase for
- I want the system to pre-populate commodity and location from the mission, so that I don't make errors
- I want to enter quantity and unit price, so that the system can calculate the total amount
- I want the system to automatically calculate payment amount (quantity × unit price), so that calculations are accurate
- I want to optionally assign a customer to the transaction, so that customer relationships can be tracked
- I want to add remarks to transactions, so that I can include additional context
- I want to receive confirmation when transactions are successfully created, so that I know the purchase was recorded

**As the System:**
- I want to validate that the buyer is assigned to the selected mission, so that unauthorized transactions are prevented
- I want to ensure the commodity matches the mission assignment, so that purchasing stays within scope
- I want to require location information, so that geographic data is captured
- I want to update mission actual amounts when transactions are created, so that budgets are tracked in real-time
- I want to generate unique transaction codes, so that each transaction can be uniquely identified

### Technical Implementation

#### Local Database Schema (SQLite + AsyncStorage)
```typescript
// Local SQLite Tables for Offline Storage
interface LocalTransaction {
  id: string;
  transaction_code: string;
  user_id: string;
  mission_id: string;
  commodity_id: string;
  customer_id?: string;
  location_id: string;
  quantity: number;
  unit_price: number;
  payment_amount: number;
  transaction_date: string;
  remarks?: string;
  status: 'pending' | 'completed' | 'cancelled';
  sync_status: 'pending' | 'synced' | 'conflict';
  created_at: string;
  updated_at: string;
  last_sync_at?: string;
}

// AsyncStorage Keys for Simple Data
const STORAGE_KEYS = {
  USER_DATA: '@DCBuyer:user',
  MISSIONS: '@DCBuyer:missions',
  SYNC_QUEUE: '@DCBuyer:sync_queue',
  LAST_SYNC: '@DCBuyer:last_sync',
  NETWORK_STATUS: '@DCBuyer:network_status'
};
```

#### Mobile App Architecture & Sync Endpoints
```typescript
// Local Data Services
class OfflineTransactionService {
  async createTransaction(transaction: LocalTransaction): Promise<void>
  async getTransactions(filters?: TransactionFilters): Promise<LocalTransaction[]>
  async updateTransaction(id: string, updates: Partial<LocalTransaction>): Promise<void>
  async deleteTransaction(id: string): Promise<void>
  async getPendingSyncTransactions(): Promise<LocalTransaction[]>
}

// Sync Service with Supabase
class SyncService {
  async syncTransactions(): Promise<SyncResult>
  async downloadMissions(): Promise<Mission[]>
  async uploadPendingTransactions(): Promise<void>
  async resolveConflicts(conflicts: DataConflict[]): Promise<void>
}

// Supabase API Endpoints (when online)
Supabase REST API:
GET    /rest/v1/transactions            # Sync transactions
POST   /rest/v1/transactions            # Upload new transactions
GET    /rest/v1/missions                # Download assigned missions
GET    /rest/v1/commodities             # Download commodity data
GET    /rest/v1/customers               # Download customer data
GET    /rest/v1/locations               # Download location data

// Real-time subscriptions
Supabase Realtime:
SUBSCRIBE transactions                   # Real-time transaction updates
SUBSCRIBE missions                       # Real-time mission updates
```

---

## 💳 TRANSACTIONS FEATURE

### Current System Implementation

**Status**: ✅ Offline-First Mobile Implementation
**Components**: `TransactionScreen.tsx`, `TransactionList.tsx`, `TransactionDetail.tsx`
**Services**: `OfflineTransactionService.ts`, `TransactionSyncService.ts`
**Local Storage**: SQLite database with WatermelonDB ORM
**Sync**: Bi-directional sync with Supabase PostgreSQL

### System Capabilities

#### Offline Transaction Management
- **Offline CRUD Operations**: Create, read, update, delete transactions without internet
- **Local Search & Filtering**: Instant search by transaction code, user, mission, date range, status
- **Offline Pagination**: Efficient handling of large local transaction datasets
- **Local Statistics Dashboard**: Real-time transaction summaries calculated from local data
- **Offline Relationship Management**: Full integration with locally cached users, missions, commodities, customers, locations
- **Background Sync**: Automatic upload/download when network is available
- **Sync Conflict Resolution**: Handle conflicts between local and server data

#### Offline Data Validation & Integrity
- **Local Mission Authorization**: Validate buyer permissions using cached mission data
- **Offline Commodity Validation**: Ensure transaction commodity matches mission using local data
- **Instant Calculations**: Payment amount = quantity × unit price calculated locally
- **Local Budget Tracking**: Real-time updates to mission budgets stored locally
- **Offline Code Generation**: Generate unique transaction codes locally with sync validation
- **Data Consistency Checks**: Validate data integrity before sync operations

### Detailed User Stories

#### Epic: Transaction Lifecycle Management

**As an Administrator:**
- I want to view all transactions offline in a paginated list, so that I can efficiently browse large datasets without internet
- I want to filter transactions by multiple criteria using local search, so that I can find specific transactions instantly
- I want to see real-time transaction statistics calculated from local data, so that I can analyze performance anywhere
- I want to view detailed transaction information stored locally, so that I can review purchase details offline
- I want to edit transaction details offline with automatic sync, so that I can correct errors immediately
- I want to delete transactions locally with sync queue management, so that I can remove invalid entries
- I want to see complete transaction relationships from cached data, so that I have full context offline
- I want to see sync status for each transaction, so that I know what data needs to be uploaded

**As a Buyer:**
- I want to view my transaction history instantly from local storage, so that I can track activities without network delays
- I want to see transaction details including amounts and dates offline, so that I can review purchases anywhere
- I want to filter my transactions locally by date or mission, so that I can find specific purchases instantly
- I want to see transaction status including sync status, so that I know if purchases are pending, completed, or need sync
- I want to create transactions offline, so that I can record purchases in areas with poor connectivity
- I want automatic background sync, so that my transactions are uploaded when network becomes available

#### Epic: Transaction Data Integrity

**As the System:**
- I want to validate all transaction data before saving, so that data integrity is maintained
- I want to automatically generate unique transaction codes, so that each transaction can be tracked
- I want to calculate payment amounts automatically, so that calculation errors are prevented
- I want to update mission budgets in real-time, so that spending tracking is accurate
- I want to maintain audit trails, so that transaction changes can be tracked
- I want to enforce referential integrity, so that transactions always link to valid entities

### Technical Features

#### Advanced Search & Filtering
- Transaction code search
- User/buyer filtering
- Mission filtering
- Date range filtering
- Status filtering (pending, completed, cancelled)
- Commodity filtering
- Customer filtering
- Location filtering

#### Performance Optimization
- Database indexing on key fields
- Efficient pagination with limit/offset
- Query optimization for complex joins
- Caching for frequently accessed data

---

## 👥 CUSTOMERS FEATURE

### Current System Implementation

**Status**: ✅ Offline-First Mobile Implementation
**Components**: `CustomerScreen.tsx`, `CustomerList.tsx`, `CustomerForm.tsx`
**Services**: `OfflineCustomerService.ts`, `CustomerSyncService.ts`
**Local Storage**: SQLite database with local search indexing
**Sync**: Real-time sync with Supabase with conflict resolution

### System Capabilities

#### Offline Customer Management
- **Offline CRUD Operations**: Create, read, update, delete customers without internet connection
- **Local Code Generation**: Generate unique customer codes offline with sync validation
- **Cached Location Integration**: Full integration with locally stored location hierarchy
- **Offline Status Management**: Active/inactive customer status control with local updates
- **Local Transaction History**: View customer's transaction history from local database
- **Instant Search**: Search by name, code, phone, email using local search indexing
- **Background Sync**: Automatic customer data sync when network is available
- **Conflict Resolution**: Handle customer data conflicts between local and server

#### Offline Data Management
- **Local Contact Storage**: Name, phone, email, address stored in SQLite
- **Cached Geographic Data**: Integration with locally stored location hierarchy (country > province > district > LLG)
- **Local Status Tracking**: Active/inactive status management with immediate updates
- **Local Audit Trail**: Created/updated timestamps and user tracking stored locally
- **Sync Status Tracking**: Track which customer records need to be synced
- **Data Validation**: Validate customer data locally before sync operations

### Detailed User Stories

#### Epic: Customer Relationship Management

**As an Administrator:**
- I want to create new customer records, so that I can track customer relationships
- I want to view a list of all customers with pagination, so that I can efficiently manage large customer databases
- I want to search customers by name, code, phone, or email, so that I can quickly find specific customers
- I want to edit customer information, so that I can keep customer data up-to-date
- I want to view detailed customer profiles, so that I can see complete customer information
- I want to see customer transaction history, so that I can understand customer purchasing patterns
- I want to activate/deactivate customers, so that I can control which customers are available for transactions
- I want to assign customers to specific locations, so that geographic relationships are maintained

**As a Buyer:**
- I want to search for customers when creating transactions, so that I can assign purchases to the right customers
- I want to see customer contact information, so that I can communicate with customers if needed
- I want to view customer location information, so that I know where customers are located
- I want to see customer status, so that I know if customers are active for transactions

#### Epic: Customer Data Management

**As an Administrator:**
- I want customer codes to be automatically generated, so that each customer has a unique identifier
- I want to store complete customer contact information, so that customers can be reached when needed
- I want to integrate customers with the location hierarchy, so that geographic relationships are maintained
- I want to track when customers are created and updated, so that I have audit information
- I want to soft delete customers, so that historical transaction data is preserved
- I want to validate customer email addresses, so that contact information is accurate
- I want to ensure customer codes and emails are unique, so that duplicate customers are prevented

#### Epic: Customer Transaction Integration

**As the System:**
- I want to link transactions to customers optionally, so that customer relationships can be tracked
- I want to show customer transaction history, so that purchasing patterns can be analyzed
- I want to validate customer status before allowing transactions, so that only active customers can be used
- I want to maintain referential integrity between customers and transactions, so that data consistency is preserved

### Technical Features

#### Customer Code Generation
- Automatic unique code generation
- Configurable code format
- Collision detection and retry logic

#### Location Integration
- Full integration with location hierarchy
- Cascading dropdown support
- Geographic data validation

#### Search & Filtering
- Multi-field search (name, code, phone, email)
- Location-based filtering
- Status filtering (active/inactive)
- Date-based filtering (creation date)

#### Performance Features
- Efficient pagination
- Database indexing on search fields
- Optimized queries for customer lists
- Caching for frequently accessed data

---

## 🔗 FEATURE INTEGRATION

### Cross-Feature Relationships

#### Buy ↔ Transactions
- Buy feature creates transactions through mission-based workflow
- Transactions validate against mission assignments
- Budget tracking flows from transactions back to missions

#### Transactions ↔ Customers
- Transactions can optionally be assigned to customers
- Customer transaction history shows all related purchases
- Customer status affects transaction creation

#### Buy ↔ Customers
- Buyers can assign customers to transactions during purchase workflow
- Customer location data can influence mission location requirements

### Data Flow Architecture

```
Mission Assignment → Buyer Dashboard → Transaction Creation → Customer Assignment → Budget Update
     ↓                    ↓                    ↓                    ↓                ↓
  Admin Control    Buyer Interface    Transaction Record    Customer Tracking    Real-time Updates
```

---

## � OFFLINE-FIRST ARCHITECTURE

### Local Data Storage Strategy

#### AsyncStorage (Simple Key-Value Data)
- **User Authentication**: Login credentials and user profile data
- **App Settings**: User preferences, theme settings, language selection
- **Sync Metadata**: Last sync timestamps, sync queue status
- **Network State**: Connection status, sync indicators

#### SQLite Database (Complex Relational Data)
- **Transactions**: Complete transaction records with relationships
- **Customers**: Customer profiles with contact information
- **Missions**: Assigned missions with budget tracking
- **Commodities**: Commodity master data with pricing
- **Locations**: Geographic hierarchy data
- **Sync Queue**: Pending operations for server sync

#### WatermelonDB (Reactive Database)
- **Real-time Updates**: Reactive queries for UI updates
- **Performance**: Optimized for large datasets
- **Relationships**: Complex data relationships with lazy loading
- **Sync Integration**: Built-in sync capabilities with conflict resolution

### Sync Strategy & Conflict Resolution

#### Background Sync Service
```typescript
class BackgroundSyncService {
  // Automatic sync when network is available
  async performBackgroundSync(): Promise<SyncResult>

  // Handle sync conflicts with user input
  async resolveConflicts(conflicts: DataConflict[]): Promise<void>

  // Queue operations for later sync
  async queueOperation(operation: SyncOperation): Promise<void>

  // Monitor network status
  onNetworkStatusChange(callback: (isOnline: boolean) => void): void
}
```

#### Conflict Resolution Strategies
- **Last Write Wins**: For non-critical data updates
- **User Choice**: Present conflicts to user for manual resolution
- **Merge Strategy**: Automatically merge non-conflicting changes
- **Server Priority**: Server data takes precedence for critical business data

### Network State Management

#### Connection Monitoring
- **Real-time Network Detection**: Monitor WiFi, cellular, and offline states
- **Sync Status Indicators**: Visual feedback on data sync status
- **Offline Mode UI**: Adapted interface for offline operations
- **Retry Mechanisms**: Automatic retry for failed sync operations

#### Data Synchronization
- **Incremental Sync**: Only sync changed data to minimize bandwidth
- **Batch Operations**: Group multiple operations for efficient sync
- **Priority Queue**: Prioritize critical data for sync
- **Compression**: Compress data for faster sync over slow connections

## 📱 MOBILE-SPECIFIC FEATURES

### React Native + Expo Capabilities

#### Cross-Platform Development
- **iOS & Android**: Single codebase for both platforms
- **Web Support**: Progressive Web App (PWA) capabilities
- **Native Performance**: Native module access for optimal performance
- **Hot Reloading**: Fast development with instant code updates

#### Mobile-Optimized UI/UX
- **Touch-First Design**: Optimized for finger navigation and gestures
- **Responsive Layouts**: Adaptive design for various screen sizes
- **Native Animations**: Smooth transitions using React Native Reanimated
- **Haptic Feedback**: Tactile feedback for user interactions
- **Gesture Support**: Swipe, pinch, and other native gestures

#### Device Integration
- **Camera Access**: Capture photos for transaction documentation
- **GPS Location**: Automatic location detection for transactions
- **File System**: Local file storage for images and documents
- **Secure Storage**: Encrypted storage for sensitive data
- **Push Notifications**: Real-time notifications for sync status and updates

### Performance Optimization

#### Local-First Performance
- **Instant UI Updates**: Immediate response to user actions
- **Lazy Loading**: Load data as needed to conserve memory
- **Image Optimization**: Compressed images with Expo Image
- **Database Indexing**: Optimized SQLite queries for fast search
- **Memory Management**: Efficient memory usage for large datasets

#### Network Optimization
- **Minimal Data Transfer**: Only sync changed data
- **Request Batching**: Group API calls for efficiency
- **Caching Strategy**: Intelligent caching of frequently accessed data
- **Offline Indicators**: Clear visual feedback on connection status
- **Background Sync**: Non-blocking sync operations

### Security & Data Protection

#### Local Data Security
- **Encrypted Storage**: Sensitive data encrypted at rest
- **Secure Authentication**: Biometric authentication support
- **Data Validation**: Client-side validation before sync
- **Access Control**: Role-based access to features and data

#### Sync Security
- **JWT Authentication**: Secure API communication
- **Data Integrity**: Checksums and validation during sync
- **Conflict Detection**: Prevent data corruption during sync
- **Audit Trail**: Track all data changes for accountability

---

## 🎯 IMPLEMENTATION ROADMAP

### Phase 1: Core Offline Infrastructure (Completed)
- ✅ React Native + Expo setup
- ✅ AsyncStorage for user authentication
- ✅ Basic navigation with Expo Router
- ✅ Theme system with dark/light mode support

### Phase 2: Local Database Implementation (In Progress)
- 🔄 SQLite database setup
- 🔄 WatermelonDB integration
- 🔄 Local data models for transactions, customers, missions
- 🔄 Offline CRUD operations

### Phase 3: Sync Infrastructure (Planned)
- 📋 Supabase integration
- 📋 Background sync service
- 📋 Conflict resolution system
- 📋 Network state management

### Phase 4: Feature Implementation (Planned)
- 📋 Offline Buy feature
- 📋 Offline Transactions management
- 📋 Offline Customer management
- 📋 Local search and filtering

### Phase 5: Advanced Features (Future)
- 📋 Camera integration for documentation
- 📋 GPS location services
- 📋 Push notifications
- 📋 Advanced analytics and reporting

---

*Document Version: 2.0*
*Last Updated: 2025-09-28*
*System: DCBuyer - React Native + Expo with Offline-First Architecture*
